<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pointalist Fade</title>
    <link rel="preload" href="assets/BemboStd-ExtraBold.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="stylesheet" href="assets/fonts.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #1a1a1a;
            overflow: hidden;
            font-family: Arial, sans-serif;
        }

        .quadrant {
            position: absolute;
            width: 33vw;
            height: 33vh;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #ccc;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            background-color: #333;
            border: 1px solid #555;
            transition: background-color 0.3s;
        }

        .quadrant:hover {
            background-color: #444;
            color: white;
        }

        .top-left {
            top: 20px;
            left: 20px;
        }

        .top-right {
            top: 20px;
            right: 20px;
        }

        .bottom-left {
            bottom: 20px;
            left: 20px;
        }

        .bottom-right {
            bottom: 20px;
            right: 20px;
        }

        canvas {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
            background-color: #000;
            border: 1px solid #333;
        }
    </style>
</head>
<body>
    <!-- 4 Quadrant boxes for demo -->
    <div class="quadrant top-left" onclick="demoFadeOutText()">
        FADE OUT<br>TEXT
    </div>
    <div class="quadrant top-right" onclick="demoFadeInText()">
        FADE IN<br>TEXT
    </div>
    <div class="quadrant bottom-left" onclick="demoFadeOutImage()">
        FADE OUT<br>IMAGE
    </div>
    <div class="quadrant bottom-right" onclick="demoFadeInImage()">
        FADE IN<br>IMAGE
    </div>

    <!-- Canvas in center -->
    <canvas id="transitionCanvas"></canvas>

    <!-- Font test element -->
    <div id="fontTest" style="position: absolute; top: 10px; left: 10px; font-family: 'Bembo STD', serif; font-weight: 800; font-size: 24px; color: white; background: rgba(0,0,0,0.5); padding: 10px;">
        FONT TEST: BEMBO STD
    </div>

    <!-- Include the pointalist fade effect library -->
    <script src="pointalist-fade.js"></script>

    <script>
        // Initialize canvas
        const canvas = document.getElementById('transitionCanvas');
        canvas.width = 600;
        canvas.height = 400;

        // Preload font when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Check what the test element's computed font actually is
            const testElement = document.getElementById('fontTest');
            const computedStyle = window.getComputedStyle(testElement);
            console.log('Test element computed font-family:', computedStyle.fontFamily);
            console.log('Test element computed font-weight:', computedStyle.fontWeight);
            console.log('Test element computed font-size:', computedStyle.fontSize);
            console.log('Full font specification would be:', computedStyle.fontWeight + ' ' + computedStyle.fontSize + ' ' + computedStyle.fontFamily);

            if (document.fonts && document.fonts.load) {
                document.fonts.load('800 48px "Bembo STD", serif').then(() => {
                    console.log('Bembo STD font loaded and ready');
                }).catch(e => {
                    console.log('Font loading failed:', e);
                });
            }
        });

        // Simple one-line function calls  - direction, duration, pointCount, canvas, content
        function demoFadeOutText() {
            pointalistFade('out', 2000, 1000, canvas, 'Hello there');
        }

        function demoFadeInText() {
            pointalistFade('in', 2000, 1000, canvas, 'Hello there');
        }

        function demoFadeOutImage() {
            pointalistFade('out', 3000, 8000, canvas, 'images/testeremoji.png');
        }

        function demoFadeInImage() {
            pointalistFade('in', 3000, 8000, canvas, 'images/testeremoji.png');
        }
    </script>
</body>
</html>
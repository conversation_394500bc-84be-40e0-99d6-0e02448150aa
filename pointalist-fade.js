/**
 * Pointalist Fade Effect
 * A reusable JavaScript library for creating pointillistic fade transitions
 * 
 * Usage: pointalistFade(direction, duration, pointCount, targetCanvas, content)
 * 
 * @param {string} direction - 'in' or 'out'
 * @param {number} duration - Animation duration in milliseconds
 * @param {number} pointCount - Number of points to generate
 * @param {HTMLCanvasElement} targetCanvas - Canvas element to render on
 * @param {string|HTMLImageElement} content - Text string or image element
 */

class PointillisticTransition {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.points = [];
        this.animationId = null;
        this.isAnimating = false;
    }

    // Sample pixels from a canvas and create points
    samplePixels(sourceCanvas, pointCount) {
        const sourceCtx = sourceCanvas.getContext('2d');
        const imageData = sourceCtx.getImageData(0, 0, sourceCanvas.width, sourceCanvas.height);
        const data = imageData.data;
        const points = [];
        const sampledPixels = [];

        // First pass: collect all non-transparent pixels
        for (let y = 0; y < sourceCanvas.height; y += 2) { // Skip every other pixel for performance
            for (let x = 0; x < sourceCanvas.width; x += 2) {
                const index = (y * sourceCanvas.width + x) * 4;
                const alpha = data[index + 3];
                
                if (alpha > 50) { // Only include visible pixels
                    sampledPixels.push({
                        x: x,
                        y: y,
                        r: data[index],
                        g: data[index + 1],
                        b: data[index + 2],
                        a: alpha
                    });
                }
            }
        }

        // Second pass: randomly select points from sampled pixels
        const targetCount = Math.min(pointCount, sampledPixels.length);
        const selectedIndices = new Set();
        
        while (points.length < targetCount && selectedIndices.size < sampledPixels.length) {
            const randomIndex = Math.floor(Math.random() * sampledPixels.length);
            
            if (!selectedIndices.has(randomIndex)) {
                selectedIndices.add(randomIndex);
                const pixel = sampledPixels[randomIndex];
                points.push({
                    originalX: pixel.x,
                    originalY: pixel.y,
                    currentX: pixel.x,
                    currentY: pixel.y,
                    targetX: pixel.x,
                    targetY: pixel.y,
                    startX: pixel.x, // For fade-in starting position
                    startY: pixel.y, // For fade-in starting position
                    startZ: 0, // For fade-in Z starting position
                    currentZ: 0, // Current Z position for perspective
                    currentSize: Math.random() * 2 + 1, // Current size for scaling
                    color: `rgba(${pixel.r}, ${pixel.g}, ${pixel.b}, ${pixel.a / 255})`,
                    size: Math.random() * 2 + 1,
                    opacity: 1,
                    delay: Math.random() * 0.7 // Staggered delay (0-70% of animation)
                });
            }
        }

        return points;
    }

    // Create a canvas from text - synchronous version that waits for font
    createTextCanvas(text, font = null, color = null) {
        // Use the EXACT font specification that works in the DOM
        const finalFont = font || '800 48px "Bembo STD", serif';
        const finalColor = color || '#ffffff';

        // Convert to uppercase for consistent styling
        text = text.toUpperCase();

        const tempCanvas = document.createElement('canvas');
        const tempCtx = tempCanvas.getContext('2d');

        // Force a synchronous font load by creating a hidden element
        const hiddenDiv = document.createElement('div');
        hiddenDiv.style.fontFamily = '"Bembo STD", serif';
        hiddenDiv.style.fontWeight = '800';
        hiddenDiv.style.fontSize = '48px';
        hiddenDiv.style.position = 'absolute';
        hiddenDiv.style.left = '-9999px';
        hiddenDiv.style.visibility = 'hidden';
        hiddenDiv.textContent = text;
        document.body.appendChild(hiddenDiv);

        // Force layout calculation to trigger font loading
        const computedStyle = window.getComputedStyle(hiddenDiv);
        const offsetWidth = hiddenDiv.offsetWidth; // Force layout
        console.log('Forced font load, computed font:', computedStyle.fontFamily, 'width:', offsetWidth);

        document.body.removeChild(hiddenDiv);

        // Set font and measure
        tempCtx.font = finalFont;
        console.log('Canvas font set to:', tempCtx.font);

        const metrics = tempCtx.measureText(text);
        const width = Math.ceil(metrics.width) + 40;
        const height = 80;

        // Set canvas size - try with device pixel ratio for crispness
        const dpr = window.devicePixelRatio || 1;
        tempCanvas.width = width * dpr;
        tempCanvas.height = height * dpr;
        tempCanvas.style.width = width + 'px';
        tempCanvas.style.height = height + 'px';

        // Scale context
        tempCtx.scale(dpr, dpr);

        // Configure text rendering
        tempCtx.font = finalFont;
        tempCtx.fillStyle = finalColor;
        tempCtx.textAlign = 'center';
        tempCtx.textBaseline = 'middle';

        // Try different text rendering settings
        tempCtx.textRenderingOptimization = 'optimizeQuality';

        // Render text
        tempCtx.fillText(text, width / 2, height / 2);

        console.log('Canvas created successfully');
        return tempCanvas;
    }

    // Create a canvas from an image element
    createImageCanvas(imageElement) {
        const tempCanvas = document.createElement('canvas');
        const tempCtx = tempCanvas.getContext('2d');
        
        tempCanvas.width = imageElement.width || imageElement.naturalWidth;
        tempCanvas.height = imageElement.height || imageElement.naturalHeight;
        
        tempCtx.drawImage(imageElement, 0, 0, tempCanvas.width, tempCanvas.height);
        
        return tempCanvas;
    }

    // Fade out animation
    fadeOut(sourceCanvas, duration, pointCount, maxScatter = 200) {
        if (this.isAnimating) return;

        this.points = this.samplePixels(sourceCanvas, pointCount);

        // Set up canvas
        this.canvas.width = sourceCanvas.width + maxScatter * 2;
        this.canvas.height = sourceCanvas.height + maxScatter * 2;
        const offsetX = maxScatter;
        const offsetY = maxScatter;

        // Set random scatter targets for each point with staggered delays
        this.points.forEach(point => {
            const angle = Math.random() * Math.PI * 2;
            const distance = Math.random() * maxScatter;
            point.targetX = point.originalX + Math.cos(angle) * distance;
            point.targetY = point.originalY + Math.sin(angle) * distance;
            point.currentX = point.originalX;
            point.currentY = point.originalY;
            point.opacity = 1;
            // More varied delay distribution for fade out
            point.delay = Math.random() * 0.8;
        });

        this.animate(duration, offsetX, offsetY, 'fadeOut');
    }

    // Fade in animation
    fadeIn(sourceCanvas, duration, pointCount, maxScatter = 200) {
        if (this.isAnimating) return;

        this.points = this.samplePixels(sourceCanvas, pointCount);

        // Set up canvas
        this.canvas.width = sourceCanvas.width + maxScatter * 2;
        this.canvas.height = sourceCanvas.height + maxScatter * 2;
        const offsetX = maxScatter;
        const offsetY = maxScatter;

        // Set up points to fly in from distance like in your original code
        this.points.forEach(point => {
            // Calculate starting position (scattered around the final position)
            const angle = Math.random() * Math.PI * 2;
            const distance = maxScatter * (0.5 + Math.random() * 1.5);

            // Starting position (far from target)
            point.startX = point.originalX + Math.cos(angle) * distance;
            point.startY = point.originalY + Math.sin(angle) * distance;
            point.startZ = -1000 - Math.random() * 2000; // Start far back in Z

            // Current position starts at starting position
            point.currentX = point.startX;
            point.currentY = point.startY;
            point.currentZ = point.startZ;

            // Target is the original text position
            point.targetX = point.originalX;
            point.targetY = point.originalY;

            // Start invisible
            point.opacity = 0;

            // Staggered delays for more natural materialization
            point.delay = Math.random() * 0.6;
        });

        this.animate(duration, offsetX, offsetY, 'fadeIn');
    }

    // Main animation loop
    animate(duration, offsetX, offsetY, type) {
        this.isAnimating = true;
        const startTime = performance.now();
        const fadeTransitionTime = 300; // 300ms for gradient fade transitions
        const totalDuration = duration + (type === 'fadeIn' ? fadeTransitionTime : 0);

        const animateFrame = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const totalProgress = Math.min(elapsed / totalDuration, 1);

            // Clear canvas
            this.ctx.fillStyle = '#000000';
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

            // Calculate gradient fade multiplier
            let gradientFade = 1;
            if (type === 'fadeOut') {
                // Fade out: start with gradient fade-in, then normal animation
                if (elapsed < fadeTransitionTime) {
                    gradientFade = elapsed / fadeTransitionTime;
                }
            } else { // fadeIn
                // Fade in: normal animation, then gradient fade to final image
                if (progress >= 1 && elapsed > duration) {
                    // Final transition phase
                    const finalPhase = (elapsed - duration) / fadeTransitionTime;
                    const finalProgress = Math.min(finalPhase, 1);

                    // Draw points with decreasing opacity
                    this.points.forEach(point => {
                        const pointOpacity = Math.max(0, 1 - finalProgress);
                        if (pointOpacity > 0.01) {
                            this.ctx.globalAlpha = pointOpacity;
                            this.ctx.fillStyle = point.color;
                            this.ctx.fillRect(
                                point.currentX + offsetX - point.size/2,
                                point.currentY + offsetY - point.size/2,
                                point.size,
                                point.size
                            );
                        }
                    });

                    // Draw final image with increasing opacity
                    this.ctx.globalAlpha = finalProgress;
                    this.ctx.drawImage(this.sourceCanvas, offsetX, offsetY);
                    this.ctx.globalAlpha = 1;

                    if (totalProgress < 1) {
                        this.animationId = requestAnimationFrame(animateFrame);
                    } else {
                        this.isAnimating = false;
                    }
                    return;
                }
            }

            // Update and draw points
            this.points.forEach(point => {
                // Calculate individual point progress with staggered timing
                const adjustedProgress = Math.max(0, Math.min(1, (progress - point.delay) / (1 - point.delay)));

                if (type === 'fadeOut') {
                    // Easing function for fade out (ease-in)
                    const easeProgress = adjustedProgress * adjustedProgress;

                    // Update position (scatter outward)
                    point.currentX = point.originalX + (point.targetX - point.originalX) * easeProgress;
                    point.currentY = point.originalY + (point.targetY - point.originalY) * easeProgress;

                    // Staggered fade out with individual timing
                    point.opacity = Math.max(0, 1 - (adjustedProgress * 1.5));

                } else { // fadeIn
                    // Easing function for fade in (ease-out)
                    const easeProgress = 1 - Math.pow(1 - adjustedProgress, 2);

                    // Update position (fly toward screen from distance)
                    point.currentX = point.startX + (point.originalX - point.startX) * easeProgress;
                    point.currentY = point.startY + (point.originalY - point.startY) * easeProgress;
                    point.currentZ = point.startZ + (0 - point.startZ) * easeProgress;

                    // Calculate scale based on Z position (perspective effect)
                    const scale = Math.max(0.1, 1 + (point.currentZ / 1000));
                    point.currentSize = point.size * scale;

                    // Staggered fade in with individual timing
                    point.opacity = Math.min(1, adjustedProgress * 2);
                }

                // Apply gradient fade multiplier
                const finalOpacity = point.opacity * gradientFade;

                // Only draw if point should be visible
                if (finalOpacity > 0.01) {
                    this.ctx.globalAlpha = finalOpacity;
                    this.ctx.fillStyle = point.color;

                    // Draw square points instead of circles
                    const size = type === 'fadeIn' ? point.currentSize : point.size;
                    this.ctx.fillRect(
                        point.currentX + offsetX - size/2,
                        point.currentY + offsetY - size/2,
                        size,
                        size
                    );
                }
            });

            this.ctx.globalAlpha = 1;

            if (progress < 1) {
                this.animationId = requestAnimationFrame(animateFrame);
            } else {
                if (type === 'fadeOut') {
                    this.isAnimating = false;
                } else {
                    // Continue to final transition phase for fadeIn
                    this.animationId = requestAnimationFrame(animateFrame);
                }
            }
        };

        this.animationId = requestAnimationFrame(animateFrame);
    }

    // Stop current animation
    stop() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
        this.isAnimating = false;
    }

    // Clear canvas
    clear() {
        this.ctx.fillStyle = '#000000';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }
}

// Global transition instance
let globalTransition = null;

/**
 * Main function for pointalist fade effect
 * @param {string} direction - 'in' or 'out'
 * @param {number} duration - Animation duration in milliseconds
 * @param {number} pointCount - Number of points to generate
 * @param {HTMLCanvasElement} targetCanvas - Canvas element to render on
 * @param {string|HTMLImageElement} content - Text string, image path, or image element
 */
function pointalistFade(direction, duration, pointCount, targetCanvas, content) {
    // Initialize or reuse transition instance
    if (!globalTransition || globalTransition.canvas !== targetCanvas) {
        globalTransition = new PointillisticTransition(targetCanvas);
    }

    // Stop any current animation
    globalTransition.stop();

    // Create source canvas based on content type
    if (typeof content === 'string') {
        // Check if it's an image path (contains common image extensions)
        if (content.match(/\.(jpg|jpeg|png|gif|bmp|webp)$/i)) {
            // Image path - create image element and load it
            const img = new Image();
            img.crossOrigin = 'anonymous'; // Try to handle CORS
            img.onload = function() {
                const sourceCanvas = globalTransition.createImageCanvas(img);
                executeAnimation(sourceCanvas, direction, duration, pointCount);
            };
            img.onerror = function() {
                console.error('Failed to load image:', content);
                console.error('CORS Error: Try serving the files from a local server instead of opening directly in browser');
            };
            img.src = content;
        } else {
            // Text content
            const sourceCanvas = globalTransition.createTextCanvas(content);
            executeAnimation(sourceCanvas, direction, duration, pointCount);
        }
    } else if (content instanceof HTMLImageElement) {
        // Image element - ensure image is loaded
        if (content.complete && content.naturalWidth > 0) {
            // Image is already loaded
            const sourceCanvas = globalTransition.createImageCanvas(content);
            executeAnimation(sourceCanvas, direction, duration, pointCount);
        } else {
            // Wait for image to load
            content.onload = function() {
                const sourceCanvas = globalTransition.createImageCanvas(content);
                executeAnimation(sourceCanvas, direction, duration, pointCount);
            };
            content.onerror = function() {
                console.error('Failed to load image:', content.src);
            };
        }
    } else {
        console.error('Content must be a string (text/image path) or HTMLImageElement (image)');
        return;
    }

    function executeAnimation(sourceCanvas, direction, duration, pointCount) {
        // Store source canvas for fade-in final render
        globalTransition.sourceCanvas = sourceCanvas;

        // Execute the appropriate animation
        const maxScatter = 200; // Default scatter distance

        if (direction === 'out') {
            globalTransition.fadeOut(sourceCanvas, duration, pointCount, maxScatter);
        } else if (direction === 'in') {
            globalTransition.fadeIn(sourceCanvas, duration, pointCount, maxScatter);
        } else {
            console.error('Direction must be "in" or "out"');
        }
    }
}
